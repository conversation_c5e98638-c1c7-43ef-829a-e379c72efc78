from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login
from django.contrib import messages
from item.models import Category, Item
from .forms import SignupForm

# Create your views here.
def index(request):
    items = Item.objects.filter(is_sold=False)[0:6] 
    categories = Category.objects.all()

    return render(request, "core/index.html", 
                  {'categories': categories,
                   'items': items,
                  })

def contact(request):
    return render(request, 'core/contact.html')

def signup(request):
    if request.method == 'POST':
        form = SignupForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, 'Account created successfully!')
            return redirect('core:login')  # Only redirect if form is valid
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = SignupForm()

    return render(request, 'core/signup.html', {
        'form': form
    })

def login_view(request):
    return render(request, 'core/login.html')