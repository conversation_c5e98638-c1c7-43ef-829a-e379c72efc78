{% extends 'core/base.html'%}

{% block title %}Signup{% endblock title%}

{% block content %}
<div class="w-1/2 my-6 mx-auto p-6 bg-gray-100 rounded-xl">
    <h1 class="mb-6 text-3xl">Sign up</h1>
    <form action="" method="post">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="" class="inline-block mb-2">Username <br>
                {{ form.username }}
            </label>
        </div>

        <div class="mb-3">
            <label for="" class="inline-block mb-2">Email <br>
                {{ form.email }}
            </label>
        </div>

        <div class="mb-3">
            <label for="" class="inline-block mb-2">Password <br>
                {{ form.password1 }}
            </label>
        </div>

        <div class="mb-3">
            <label for="" class="inline-block mb-2">Repeat Password <br>
                {{ form.password2 }}
            </label>
        </div>

        {% if form.errors or form.non_field_errors %}
            <div class="mb-3 p-6"></div>>
                {% for field in form %}
                    {{field.errors}}
                {% endfor %}
                {{ form.non_field_errors}}
            </div>
        {% endif %}

        <button class="py-3 px-6 text-lg bg-teal-500 hover:bg-teal-700 rounded-xl text-white">Submit</button>
    </form>
</div>
{% endblock content %}