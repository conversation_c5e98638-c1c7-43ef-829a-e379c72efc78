{% extends 'core/base.html'%}

{% block title %}Signup{% endblock title%}

{% block content %}
<style>
input[type="text"], input[type="email"], input[type="password"] {
    width: 100% !important;
    box-sizing: border-box !important;
    display: block !important;
}
input::placeholder {
    color: #9CA3AF !important;
    opacity: 1 !important;
    text-align: left !important;
    width: 100% !important;
}
/* Fix for Firefox */
::-moz-placeholder {
    text-align: left !important;
    width: 100% !important;
}
/* Fix for Chrome/Edge/Safari */
::-webkit-input-placeholder {
    text-align: left !important;
    width: 100% !important;
}
</style>
<div class="my-6 mx-auto bg-gray-100 p-6 rounded-xl w-1/2">
    <h1 class="mb-6 text-3xl">Sign up</h1>
    <form action="." method="post">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="" class="inline-block mb-2 w-full">Username <br>
                {{ form.username }}
            </label>
        </div>

        <div class="mb-3">
            <label for="" class="inline-block mb-2 w-full">Email <br>
                {{ form.email }}
            </label>
        </div>

        <div class="mb-3">
            <label for="" class="inline-block mb-2 w-full">Password <br>
                {{ form.password1 }}
            </label>
        </div>

        <div class="mb-3">
            <label for="" class="inline-block mb-2 w-full">Repeat Password <br>
                {{ form.password2 }}
            </label>
        </div>

        {% if form.errors or form.non_field_errors %}
            <div class="mb-3 p-6 bg-red-100 rounded-xl"></div>>
                {% for field in form %}
                    {{field.errors}}
                {% endfor %}
                {{ form.non_field_errors}}
            </div>
        {% endif %}

        <button class="py-3 px-6 text-lg bg-teal-500 hover:bg-teal-700 rounded-xl text-white">Submit</button>
    </form>
</div>
{% endblock content %}