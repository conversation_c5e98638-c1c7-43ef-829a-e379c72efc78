from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User

class SignupForm(UserCreationForm):
    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2')

    username = forms.CharField(widget=forms.TextInput(attrs={
        'placeholder' : "Your username...",
        'class' : "block w-full py-4 px-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-teal-500",
        'style' : "width: 100%; box-sizing: border-box;"
    }))
    email = forms.CharField(widget=forms.EmailInput(attrs={
        'placeholder' : "Your email...",
        'class' : "block w-full py-4 px-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-teal-500",
        'style' : "width: 100%; box-sizing: border-box;"
    }))
    password1 = forms.CharField(widget = forms.PasswordInput(attrs={
        "placeholder" : "Your password...",
        "class" : "block w-full py-4 px-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-teal-500",
        'style' : "width: 100%; box-sizing: border-box;"
    }))
    password2 = forms.CharField(widget=forms.PasswordInput(attrs={
        "placeholder" : "Repeat password...",
        "class" : "block w-full py-4 px-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-teal-500",
        'style' : "width: 100%; box-sizing: border-box;"
    }))